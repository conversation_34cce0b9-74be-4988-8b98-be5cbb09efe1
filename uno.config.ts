// https://www.npmjs.com/package/@uni-helper/unocss-preset-uni
import { presetUni } from '@uni-helper/unocss-preset-uni'
import {
  defineConfig,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

export default defineConfig({
  presets: [
    presetUni({
      attributify: {
        // prefix: 'fg-', // 如果加前缀，则需要在代码里面使用 `fg-` 前缀，如：<div fg-border="1px solid #000"></div>
        prefixedOnly: true,
      },
    }),
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    // 支持css class属性化
    presetAttributify(),
  ],
  transformers: [
    // 启用指令功能：主要用于支持 @apply、@screen 和 theme() 等 CSS 指令
    transformerDirectives(),
    // 启用 () 分组功能
    // 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
    transformerVariantGroup(),
  ],
  shortcuts: [
    {
      center: 'flex justify-center items-center',
    },
  ],
  safelist: [
    // 项目中使用的图标
    'i-carbon-home',
    'i-carbon-tools',
    'i-carbon-user',
    'i-carbon-time',
    'i-carbon-help',
    'i-carbon-share',
    'i-carbon-chevron-right',
    'i-carbon-video',
    'i-carbon-image',
    'i-carbon-document-blank',
    'i-carbon-play',
    'i-carbon-download',
    'i-carbon-save',
    'i-carbon-send',
    'i-carbon-warning',
    'i-carbon-platforms',
    'i-carbon-help-desk',
    'i-carbon-email',
    'i-carbon-version',
    'i-carbon-fingerprint-recognition',
    'i-carbon-cloud-upload',
    'i-carbon-document',
    'i-carbon-close',
    'i-carbon-checkmark-filled',
    'i-carbon-checkmark',
    'i-carbon-trash-can',
    'i-carbon-renew',
    'i-carbon-copy',
    'i-carbon-flag',
    'i-carbon-data-1',
    'i-carbon-chevron-up',
    'i-carbon-chevron-down',
    'i-carbon-add',
    'i-carbon-view',
    // 平台图标
    'i-carbon-music',
    'i-carbon-fire',
    'i-carbon-book',
    'i-carbon-share',
    'i-carbon-document',
    'i-carbon-play',
    'i-carbon-menu',
  ],
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
  ],
  theme: {
    colors: {
      /** 主题色，用法如: text-primary */
      primary: 'var(--wot-color-theme,#0957DE)',
    },
    fontSize: {
      /** 提供更小号的字体，用法如：text-2xs */
      '2xs': ['20rpx', '28rpx'],
      '3xs': ['18rpx', '26rpx'],
    },
  },
})
