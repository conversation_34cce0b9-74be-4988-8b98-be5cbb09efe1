<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "视频详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { getCurrentInstance, onMounted, ref } from 'vue'
import { useNotify } from 'wot-design-uni'
import MediaActions from '@/components/MediaActions/MediaActions.vue'
import MediaInfo from '@/components/MediaInfo/MediaInfo.vue'
import { getSafeAreaStyle } from '@/utils/safeArea'
import { MediaType, saveSingleMediaToAlbum } from '@/utils/saveToAlbum'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 获取通知组件
const { showNotify } = useNotify()

// 响应式数据
const videoData = ref<any>(null)

// 加载状态
const isLoading = ref(false)

// 视频上下文
let videoContext: UniApp.VideoContext | null = null

// 保存到相册
async function saveToAlbum() {
  if (isLoading.value)
    return

  // 检查视频数据
  if (!videoData.value?.video_url) {
    showNotify({
      type: 'warning',
      message: '视频链接不存在',
    })
    return
  }

  isLoading.value = true
  try {
    // 使用通用的保存工具函数，禁用内部的loading显示，由组件自己控制
    await saveSingleMediaToAlbum(
      videoData.value.video_url,
      MediaType.VIDEO,
      { showProgress: false, showResult: true },
    )
  }
  catch (error) {
    showNotify({
      type: 'danger',
      message: '保存失败',
    })
  }
  finally {
    isLoading.value = false
  }
}

// 复制视频链接
function copyVideoUrl() {
  if (!videoData.value?.video_url)
    return

  uni.setClipboardData({
    data: videoData.value.video_url,
    success: () => {
      showNotify({
        type: 'success',
        message: '链接已复制',
      })
    },
  })
}

// 全屏状态变化事件
function onFullscreenChange() {
  // 全屏状态变化处理逻辑
}

// 视频错误事件
function onVideoError() {
  showNotify({
    type: 'danger',
    message: '视频播放失败',
  })
}

// 创建视频上下文
function createVideoContextInstance() {
  const instance = getCurrentInstance()
  if (instance) {
    videoContext = uni.createVideoContext('video-player', instance)
  }
}

// 页面加载时获取视频数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.data) {
    // 从URL参数中获取解析结果数据
    try {
      const parsedData = JSON.parse(decodeURIComponent(options.data))
      videoData.value = parsedData

      // 延迟创建视频上下文，确保DOM已渲染
      setTimeout(() => {
        createVideoContextInstance()
      }, 500)
    }
    catch (error) {
      showNotify({
        type: 'danger',
        message: '数据解析失败',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else {
    // 没有数据，返回上一页
    showNotify({
      type: 'warning',
      message: '缺少数据',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <wd-notify />
  <view class="page-container" :style="safeAreaStyle">
    <!-- 视频播放区域 -->
    <view class="video-container">
      <view class="video-wrapper">
        <video
          v-if="videoData?.video_url"
          id="video-player"
          :src="videoData.video_url"
          :poster="videoData.cover"
          controls
          class="video-player"
          object-fit="cover"
          show-center-play-btn
          show-play-btn
          show-fullscreen-btn
          enable-play-gesture
          enable-progress-gesture
          @fullscreenchange="onFullscreenChange"
          @error="onVideoError"
        />

        <!-- 加载状态 -->
        <view v-else class="video-loading">
          <wd-loading size="40px" />
          <text class="loading-text">
            视频加载中...
          </text>
        </view>
      </view>
    </view>
    <view class="mt-6" />
    <!-- 视频信息 -->
    <MediaInfo :media-data="videoData" />

    <!-- 操作按钮 -->
    <MediaActions
      media-type="video"
      :is-loading="isLoading"
      :media-data="videoData"
      @save="saveToAlbum"
      @copy="copyVideoUrl"
    />
  </view>
</template>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 8px;
}

.video-container {
  margin-bottom: 24px;
}

.mt-6 {
  margin-top: 24px;
}

.video-player {
  width: 100%;
  aspect-ratio: 16 / 9;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  /* 确保全屏功能不被样式影响 */
  position: relative;
  z-index: 1;
}

.video-loading {
  width: 100%;
  aspect-ratio: 16 / 9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.loading-text {
  margin-top: 12px;
  color: #999;
  font-size: 14px;
}

.info-card {
  margin-bottom: 24px;
}

.video-info-content {
  padding: 16px 0;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-label {
  width: 80px;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  flex-shrink: 0;
  line-height: 1.5;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
}

.action-card {
  margin-bottom: 24px;
}

.action-buttons-row {
  display: flex;
  gap: 16px;
  padding: 12px 0;
}

.action-btn {
  flex: 1;
  min-height: 48px;
}
</style>
