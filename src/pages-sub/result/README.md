# 视频详情页面

这个目录包含视频详情页面的相关文件。

## 文件说明

- `result.vue` - 视频详情页面主文件，用于显示解析后的视频信息和提供操作功能

## 功能特性

### 数据获取方式

页面通过 URL 参数接收视频解析结果数据：
- 支持通过 `data` 参数传递 JSON 格式的视频信息
- 自动解析和验证传入的数据
- 数据解析失败时自动返回上一页

### 保存到相册功能

已实现完整的视频保存到相册功能，包括：

1. **权限管理**
   - 自动检查相册写入权限状态
   - 首次使用时申请权限
   - 权限被拒绝时引导用户到设置页面
   - 支持重新授权流程

2. **网络视频处理**
   - 自动检测网络视频链接
   - 先下载到本地临时文件
   - 支持下载进度提示

3. **错误处理**
   - 完善的错误分类和提示
   - 权限错误、网络错误、保存错误的不同处理
   - 用户友好的错误信息显示

4. **用户体验**
   - 保存过程中显示加载提示
   - 操作完成后的成功/失败反馈
   - 保持原有界面交互逻辑

### API 使用

- `saveSingleMediaToAlbum()` - 通用的媒体保存工具函数（来自 `@/utils/saveToAlbum`）
- `MediaType.VIDEO` - 视频媒体类型枚举
- 内部使用：
  - `uni.saveVideoToPhotosAlbum()` - 保存视频到系统相册
  - `uni.authorize()` - 申请相册写入权限
  - `uni.getSetting()` - 检查权限状态
  - `uni.openSetting()` - 打开系统设置页面
  - `uni.downloadFile()` - 下载网络视频文件

### 兼容性

支持的平台：
- App (iOS/Android)
- 微信小程序
- 支付宝小程序
- 百度小程序
- 抖音小程序
- QQ小程序
- 快手小程序
- 元服务
- 小红书小程序

注意：H5 平台不支持保存到相册功能

## 实现细节

### 权限检查流程

1. 使用 `uni.getSetting()` 检查当前权限状态
2. 如果已授权，直接执行保存操作
3. 如果未授权，使用 `uni.authorize()` 申请权限
4. 如果用户拒绝，显示说明弹窗并引导到设置页面

### 视频保存流程

现在使用通用的 `saveSingleMediaToAlbum()` 工具函数：

1. 调用 `saveSingleMediaToAlbum(videoUrl, MediaType.VIDEO)`
2. 工具函数内部处理：
   - 检查视频链接是否存在
   - 检查并申请相册写入权限
   - 网络视频自动下载到本地
   - 调用 `uni.saveVideoToPhotosAlbum()` 保存视频
   - 显示操作结果反馈

### 重要说明

- `uni.saveVideoToPhotosAlbum()` API **不支持**直接保存网络视频链接
- 网络视频必须先通过 `uni.downloadFile()` 下载到本地临时文件
- 下载的临时文件会自动被系统管理，无需手动清理

### 视频链接处理

采用简化的处理方式，充分利用 uni-app 的内置能力：

1. **自动重定向处理**：
   - `uni.downloadFile()` 会自动处理 HTTP 重定向
   - 无需手动解析重定向链接
   - 支持多级重定向跳转

2. **文件格式验证**：
   - 检查下载文件的扩展名
   - 排除非视频格式文件（.json、.html、.txt）
   - 确保下载的是有效的视频文件

3. **错误处理**：
   - 下载失败时显示具体错误信息
   - 文件格式错误时提示用户
   - 完整的错误日志记录

### 错误处理

- 权限相关错误：引导用户授权
- 网络相关错误：提示网络问题
- 文件相关错误：提示文件问题
- 其他错误：通用错误提示
