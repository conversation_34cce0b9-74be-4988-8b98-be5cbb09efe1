<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "帮助与反馈",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 展开的FAQ索引
const expandedFaq = ref<number | null>(null)

// 支持的平台
const supportedPlatforms = [
  { icon: 'i-carbon-video', color: '#ff0050' },
  { icon: 'i-carbon-video', color: '#ff6600' },
  { icon: 'i-carbon-image', color: '#ff2442' },
  { icon: 'i-carbon-share', color: '#e6162d' },
  { icon: 'i-carbon-video', color: '#00a1d6' },
  { icon: 'i-carbon-video', color: '#20b954' },
]

// 切换FAQ展开状态
function toggleFaq(index: number) {
  expandedFaq.value = expandedFaq.value === index ? null : index
}
</script>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 使用说明 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <view class="mb-4 flex items-center">
        <text class="i-carbon-help mr-2 text-xl text-blue-500" />
        <text class="text-lg text-gray-800 font-semibold">
          使用说明
        </text>
      </view>

      <view class="space-y-4">
        <view class="help-item">
          <text class="help-step">
            1
          </text>
          <view class="help-content">
            <text class="help-title">
              复制分享链接
            </text>
            <text class="help-desc">
              在短视频平台复制视频或图集的分享链接
            </text>
          </view>
        </view>

        <view class="help-item">
          <text class="help-step">
            2
          </text>
          <view class="help-content">
            <text class="help-title">
              粘贴到应用
            </text>
            <text class="help-desc">
              打开趣水印，将链接粘贴到输入框中
            </text>
          </view>
        </view>

        <view class="help-item">
          <text class="help-step">
            3
          </text>
          <view class="help-content">
            <text class="help-title">
              开始解析
            </text>
            <text class="help-desc">
              点击"开始解析"按钮，等待解析完成
            </text>
          </view>
        </view>

        <view class="help-item">
          <text class="help-step">
            4
          </text>
          <view class="help-content">
            <text class="help-title">
              下载保存
            </text>
            <text class="help-desc">
              解析成功后，可以下载视频或保存图片到相册
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 支持平台 -->
    <view class="mb-6 rounded-2xl bg-white p-6 shadow-sm">
      <view class="mb-4 flex items-center">
        <text class="i-carbon-platforms mr-2 text-xl text-green-500" />
        <text class="text-lg text-gray-800 font-semibold">
          支持平台
        </text>
      </view>

      <view class="grid grid-cols-2 gap-3">
        <view
          v-for="(platform, index) in supportedPlatforms"
          :key="index"
          class="flex items-center justify-center rounded-lg bg-gray-50 p-3"
        >
          <text :class="platform.icon" class="text-lg" :style="{ color: platform.color }" />
        </view>
      </view>
    </view>

    <!-- 免责声明 -->
    <view class="mb-20 rounded-2xl bg-white p-6 shadow-sm">
      <view class="mb-4 flex items-center">
        <text class="i-carbon-warning mr-2 text-xl text-red-500" />
        <text class="text-lg text-gray-800 font-semibold">
          免责声明
        </text>
      </view>

      <text class="text-sm text-gray-600 leading-relaxed">
        本应用仅供学习交流使用，请勿用于商业用途。下载的内容版权归原作者所有，请尊重原创，合理使用。使用本应用产生的任何法律责任由用户自行承担。
      </text>
    </view>
  </view>
</template>

<style scoped>
.space-y-4 > view:not(:first-child) {
  margin-top: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.help-item {
  display: flex;
  align-items: flex-start;
}

.help-step {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.help-content {
  flex: 1;
}

.help-title {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.help-desc {
  display: block;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

.faq-item {
  border-bottom: 1px solid #f3f4f6;
}

.faq-item:last-child {
  border-bottom: none;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-3 {
  gap: 0.75rem;
}
</style>
