<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "帮助与反馈",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <view class="min-h-screen bg-gray-50" :style="safeAreaStyle">
    <!-- 使用说明 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-help text-blue-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">使用说明</text>
      </view>
      
      <view class="space-y-4">
        <view class="help-item">
          <text class="help-step">1</text>
          <view class="help-content">
            <text class="help-title">复制分享链接</text>
            <text class="help-desc">在短视频平台复制视频或图集的分享链接</text>
          </view>
        </view>
        
        <view class="help-item">
          <text class="help-step">2</text>
          <view class="help-content">
            <text class="help-title">粘贴到应用</text>
            <text class="help-desc">打开趣水印，将链接粘贴到输入框中</text>
          </view>
        </view>
        
        <view class="help-item">
          <text class="help-step">3</text>
          <view class="help-content">
            <text class="help-title">开始解析</text>
            <text class="help-desc">点击"开始解析"按钮，等待解析完成</text>
          </view>
        </view>
        
        <view class="help-item">
          <text class="help-step">4</text>
          <view class="help-content">
            <text class="help-title">下载保存</text>
            <text class="help-desc">解析成功后，可以下载视频或保存图片到相册</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 支持平台 -->
    <view class="p-6 mb-6 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-platforms text-green-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">支持平台</text>
      </view>
      
      <view class="grid grid-cols-2 gap-3">
        <view
          v-for="(platform, index) in supportedPlatforms"
          :key="index"
          class="flex items-center justify-center p-3 bg-gray-50 rounded-lg"
        >
          <text :class="platform.icon" class="text-lg" :style="{ color: platform.color }"></text>
        </view>
      </view>
    </view>
    
    <!-- 免责声明 -->
    <view class="p-6 mb-20 bg-white shadow-sm rounded-2xl">
      <view class="flex items-center mb-4">
        <text class="i-carbon-warning text-red-500 text-xl mr-2"></text>
        <text class="text-lg font-semibold text-gray-800">免责声明</text>
      </view>
      
      <text class="text-sm text-gray-600 leading-relaxed">
        本应用仅供学习交流使用，请勿用于商业用途。下载的内容版权归原作者所有，请尊重原创，合理使用。使用本应用产生的任何法律责任由用户自行承担。
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 展开的FAQ索引
const expandedFaq = ref<number | null>(null)

// 支持的平台
const supportedPlatforms = [
  { icon: 'i-carbon-video', color: '#ff0050' },
  { icon: 'i-carbon-video', color: '#ff6600' },
  { icon: 'i-carbon-image', color: '#ff2442' },
  { icon: 'i-carbon-share', color: '#e6162d' },
  { icon: 'i-carbon-video', color: '#00a1d6' },
  { icon: 'i-carbon-video', color: '#20b954' },
]

// 切换FAQ展开状态
const toggleFaq = (index: number) => {
  expandedFaq.value = expandedFaq.value === index ? null : index
}

</script>

<style scoped>
.space-y-4 > view:not(:first-child) {
  margin-top: 1rem;
}

.space-y-3 > view:not(:first-child) {
  margin-top: 0.75rem;
}

.help-item {
  display: flex;
  align-items: flex-start;
}

.help-step {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.help-content {
  flex: 1;
}

.help-title {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.help-desc {
  display: block;
  font-size: 14px;
  color: #6B7280;
  line-height: 1.5;
}

.faq-item {
  border-bottom: 1px solid #F3F4F6;
}

.faq-item:last-child {
  border-bottom: none;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-3 {
  gap: 0.75rem;
}
</style>
