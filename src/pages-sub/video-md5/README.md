# 文件MD5修改工具

这个目录包含文件MD5修改工具页面的相关文件。

## 文件说明

- `video-md5.vue` - MD5修改工具页面主文件，用于修改媒体文件的MD5值并保存到相册

## 功能特性

### 核心功能

1. **媒体文件选择**
   - 支持从相册选择图片和视频
   - 支持相机拍摄
   - 自动获取文件信息（大小、MD5等）

2. **MD5修改处理**
   - 计算原始文件的MD5值
   - 通过在文件末尾添加随机数据来修改MD5
   - 保持文件的可用性和完整性

3. **自动保存到相册**
   - 处理完成后自动保存到系统相册
   - 使用通用的保存工具函数
   - 支持图片和视频的保存

### 保存到相册功能

已集成通用的保存工具函数：

1. **权限管理**
   - 自动检查相册写入权限状态
   - 首次使用时申请权限
   - 权限被拒绝时引导用户到设置页面

2. **媒体类型支持**
   - 图片保存：使用 `MediaType.IMAGE`
   - 视频保存：使用 `MediaType.VIDEO`
   - 自动识别文件类型

3. **用户体验**
   - 自定义的成功/失败提示信息
   - 不显示工具函数的默认进度和结果提示
   - 保持原有的界面交互逻辑

### API 使用

- `saveSingleMediaToAlbum()` - 通用的媒体保存工具函数（来自 `@/utils/saveToAlbum`）
- `MediaType.IMAGE` / `MediaType.VIDEO` - 媒体类型枚举
- 内部使用：
  - `uni.saveImageToPhotosAlbum()` - 保存图片到系统相册
  - `uni.saveVideoToPhotosAlbum()` - 保存视频到系统相册
  - `uni.authorize()` - 申请相册写入权限
  - `uni.getSetting()` - 检查权限状态
  - `uni.openSetting()` - 打开系统设置页面

## 实现细节

### MD5修改原理

1. **读取原始文件**：获取文件的二进制数据
2. **计算原始MD5**：使用加密算法计算文件的MD5值
3. **添加随机数据**：在文件末尾添加少量随机字节
4. **重新计算MD5**：生成新的MD5值
5. **保存新文件**：将修改后的文件保存到临时目录

### 文件处理流程

1. 用户选择媒体文件（图片或视频）
2. 系统读取文件并计算原始MD5
3. 显示文件信息（名称、大小、MD5）
4. 用户点击"开始修改"按钮
5. 系统在文件末尾添加随机数据
6. 重新计算新的MD5值
7. 自动保存修改后的文件到相册
8. 显示处理结果

### 保存流程

现在使用通用的 `saveSingleMediaToAlbum()` 工具函数：

1. 调用 `saveSingleMediaToAlbum(filePath, mediaType, options)`
2. 工具函数内部处理：
   - 检查文件路径是否存在
   - 检查并申请相册写入权限
   - 根据媒体类型调用相应的保存API
   - 处理保存结果

3. 自定义选项：
   - `showProgress: false` - 不显示默认进度提示
   - `showResult: false` - 不显示默认结果提示
   - 使用自定义的成功/失败消息

### 错误处理

- **文件选择错误**：提示用户重新选择
- **MD5计算错误**：显示计算失败信息
- **文件修改错误**：提示修改过程中的问题
- **保存错误**：区分权限错误和其他保存错误
- **权限相关错误**：引导用户授权

## 技术特点

### 文件系统操作

- 使用 `uni.getFileSystemManager()` 进行文件操作
- 支持读取、写入和删除临时文件
- 处理大文件时的内存管理

### MD5计算

- 使用流式处理避免内存溢出
- 支持大文件的MD5计算
- 实时显示计算进度

### 随机数据生成

- 生成少量随机字节（通常1-100字节）
- 确保不影响文件的正常使用
- 保证每次修改产生不同的MD5值

## 兼容性

支持的平台：
- App (iOS/Android)
- 微信小程序
- 支付宝小程序
- 百度小程序
- 抖音小程序
- QQ小程序
- 快手小程序
- 元服务
- 小红书小程序

注意：H5 平台不支持文件系统操作和保存到相册功能

## 使用场景

1. **内容创作**：避免平台的重复内容检测
2. **文件分享**：让相同内容被识别为不同文件
3. **测试用途**：生成具有不同MD5的测试文件
4. **隐私保护**：改变文件指纹特征

## 注意事项

1. **文件完整性**：修改后的文件功能完全正常
2. **文件大小**：会略微增加文件大小（增加的随机数据）
3. **处理时间**：大文件的MD5计算可能需要较长时间
4. **存储空间**：修改后的文件会占用额外的存储空间
5. **合规使用**：请确保使用符合相关平台的服务条款

## 未来扩展

可以考虑添加的功能：
- 批量文件处理
- 自定义添加的数据量
- MD5值预览和验证
- 处理历史记录
- 文件格式转换
- 压缩选项
