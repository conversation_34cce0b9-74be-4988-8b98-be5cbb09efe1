<route lang="jsonc">
{
  "style": {
    "navigationBarTitleText": "图集详情",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { getSafeAreaStyle } from '@/utils/safeArea'
import { saveMultipleImagesToAlbum } from '@/utils/saveToAlbum'


// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const albumData = ref<any>(null)
const isDownloading = ref(false)
const selectedImages = ref<Set<number>>(new Set()) // 选中的图片索引
const isSelectMode = ref(false) // 是否处于选择模式

// 计算属性
const totalImages = computed(() => albumData.value?.images?.length || 0)
const selectedCount = computed(() => selectedImages.value.size)
const isAllSelected = computed(() =>
  totalImages.value > 0 && selectedImages.value.size === totalImages.value
)

// 处理图片点击
function handleImageClick(index: number) {
  if (isSelectMode.value) {
    // 选择模式下切换选中状态
    toggleImageSelection(index)
  } else {
    // 预览模式下打开图片预览
    if (!albumData.value?.images?.length) return

    uni.previewImage({
      urls: albumData.value.images,
      current: index,
    })
  }
}

// 切换图片选中状态
function toggleImageSelection(index: number) {
  if (selectedImages.value.has(index)) {
    selectedImages.value.delete(index)
  } else {
    selectedImages.value.add(index)
  }
}

// 切换全选状态
function toggleSelectAll() {
  if (isAllSelected.value) {
    selectedImages.value.clear()
  } else {
    selectedImages.value.clear()
    for (let i = 0; i < totalImages.value; i++) {
      selectedImages.value.add(i)
    }
  }
}

// 进入选择模式
function enterSelectMode() {
  isSelectMode.value = true
  selectedImages.value.clear()
}

// 退出选择模式
function exitSelectMode() {
  isSelectMode.value = false
  selectedImages.value.clear()
}

// 检查相册权限
async function checkPermission(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        const authSetting = res.authSetting

        // 如果已经授权，直接返回成功
        if (authSetting['scope.writePhotosAlbum'] === true) {
          resolve(true)
          return
        }

        // 如果之前拒绝过授权，引导用户到设置页面
        if (authSetting['scope.writePhotosAlbum'] === false) {
          uni.showModal({
            title: '权限申请',
            content: '需要相册写入权限来保存图片，请在设置中开启',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                uni.openSetting({
                  success: (settingRes) => {
                    resolve(settingRes.authSetting['scope.writePhotosAlbum'] === true)
                  },
                  fail: () => resolve(false),
                })
              } else {
                resolve(false)
              }
            },
          })
          return
        }

        // 首次申请权限
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => resolve(true),
          fail: () => {
            // 用户拒绝授权，显示说明
            uni.showModal({
              title: '权限申请',
              content: '需要相册写入权限来保存图片，请在设置中开启',
              confirmText: '去设置',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  uni.openSetting({
                    success: (settingRes) => {
                      resolve(settingRes.authSetting['scope.writePhotosAlbum'] === true)
                    },
                    fail: () => resolve(false),
                  })
                } else {
                  resolve(false)
                }
              },
            })
          },
        })
      },
      fail: () => {
        // 获取设置失败，直接尝试授权
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => resolve(true),
          fail: () => resolve(false),
        })
      },
    })
  })
}

// 保存选中的图片到相册
async function saveSelectedToAlbum() {
  if (isDownloading.value || selectedImages.value.size === 0) return

  // 先检查权限
  const hasPermission = await checkPermission()
  if (!hasPermission) {
    uni.showToast({
      title: '没有相册访问权限',
      icon: 'none',
    })
    return
  }

  isDownloading.value = true
  try {
    const selectedUrls = Array.from(selectedImages.value).map(index =>
      albumData.value.images[index]
    )

    // 使用工具函数保存，但不显示内置的权限检查
    await saveMultipleImagesToAlbum(selectedUrls, {
      showProgress: true,
      showResult: true
    })

    // 保存完成后退出选择模式
    exitSelectMode()
  } catch (error) {
    console.error('保存选中图片失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  } finally {
    isDownloading.value = false
  }
}



// 页面加载时获取图集数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options || {}

  if (options.data) {
    try {
      // 从首页传递过来的真实数据
      const decodedData = decodeURIComponent(options.data)
      const parsedData = JSON.parse(decodedData)
      albumData.value = parsedData
      console.log('图集数据:', parsedData)
    }
    catch (error) {
      console.error('解析图集数据失败:', error)
      uni.showToast({
        title: '数据解析失败',
        icon: 'none',
      })
      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
  else {
    // 没有数据，返回上一页
    uni.showToast({
      title: '缺少必要参数',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="min-h-screen bg-gray-50 p-2" :style="safeAreaStyle">
        <!-- 顶部操作栏 -->
        <view class="flex items-center justify-between mb-4 px-2">
          <view class="flex items-center">
            <text class="text-lg font-medium text-gray-800">
              共{{ totalImages }}张图片
            </text>
            <text v-if="isSelectMode" class="ml-2 text-sm text-blue-600">
              已选{{ selectedCount }}张
            </text>
          </view>

          <view class="flex items-center gap-2">
            <!-- 选择模式切换 -->
            <wd-button v-if="!isSelectMode"
                       type="primary"
                       size="small"
                       round
                       @click="enterSelectMode">
              选择
            </wd-button>

            <!-- 选择模式下的操作 -->
            <template v-else>
              <wd-button type="info"
                         size="small"
                         round
                         @click="exitSelectMode">
                取消
              </wd-button>
              <wd-button :type="isAllSelected ? 'primary' : 'default'"
                         size="small"
                         round
                         @click="toggleSelectAll">
                {{ isAllSelected ? '全不选' : '全选' }}
              </wd-button>
              <wd-button v-if="selectedCount > 0"
                         type="success"
                         size="small"
                         round
                         @click="saveSelectedToAlbum">
                保存至相册
              </wd-button>
            </template>
          </view>
        </view>

        <!-- 图片网格 -->
        <view v-if="albumData?.images?.length" class="grid grid-cols-2 gap-2 mb-4">
          <view v-for="(image, index) in albumData.images"
                :key="index"
                class="relative aspect-square rounded-lg overflow-hidden bg-gray-100"
                @click="handleImageClick(index)">

            <!-- 图片 -->
            <image :src="image"
                   class="w-full h-full object-cover"
                   mode="aspectFill" />

            <!-- 选择状态覆盖层 -->
            <view v-if="isSelectMode"
                  class="absolute inset-0 flex items-center justify-center"
                  :class="selectedImages.has(index) ? 'bg-blue-500 bg-opacity-50' : 'bg-black bg-opacity-20'">
              <view class="w-6 h-6 rounded-full border-2 border-white flex items-center justify-center"
                    :class="selectedImages.has(index) ? 'bg-blue-500' : 'bg-transparent'">
                <text v-if="selectedImages.has(index)" class="text-white text-xs">✓</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-else class="h-64 w-full flex flex-col items-center justify-center bg-gray-100 rounded-lg">
          <wd-loading type="ring" size="40px" />
          <text class="mt-3 text-sm text-gray-500">
            图片加载中...
          </text>
        </view>
  </view>
</template>

<style scoped>
/* 网格布局样式 */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-2 {
  gap: 8px;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

/* 选择状态动画 */
.bg-opacity-50 {
  background-color: rgba(59, 130, 246, 0.5);
}

.bg-opacity-20 {
  background-color: rgba(0, 0, 0, 0.2);
}
</style>
