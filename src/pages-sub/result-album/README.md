# 图片相册页面

这个目录包含图片相册页面的相关文件。

## 文件说明

- `result-album.vue` - 图片相册页面主文件，用于显示解析后的图片集合和提供批量保存功能

## 功能特性

### 数据获取方式

页面通过 URL 参数接收图片相册解析结果数据：
- 支持通过 `data` 参数传递 JSON 格式的图片相册信息
- 自动解析和验证传入的数据
- 数据解析失败时自动返回上一页

### 批量保存到相册功能

已实现完整的批量图片保存到相册功能，使用通用的保存工具函数：

1. **权限管理**
   - 自动检查相册写入权限状态
   - 首次使用时申请权限
   - 权限被拒绝时引导用户到设置页面
   - 支持重新授权流程

2. **批量处理**
   - 支持一次性保存多张图片
   - 实时显示保存进度
   - 详细的成功/失败统计

3. **错误处理**
   - 完善的错误分类和提示
   - 权限错误、网络错误、保存错误的不同处理
   - 用户友好的错误信息显示

4. **用户体验**
   - 保存过程中显示进度提示
   - 操作完成后的详细结果反馈
   - 保持原有界面交互逻辑

### API 使用

- `saveMultipleImagesToAlbum()` - 批量图片保存工具函数（来自 `@/utils/saveToAlbum`）
- 内部使用：
  - `uni.saveImageToPhotosAlbum()` - 保存单张图片到系统相册
  - `uni.authorize()` - 申请相册写入权限
  - `uni.getSetting()` - 检查权限状态
  - `uni.openSetting()` - 打开系统设置页面

### 兼容性

支持的平台：
- App (iOS/Android)
- 微信小程序
- 支付宝小程序
- 百度小程序
- 抖音小程序
- QQ小程序
- 快手小程序
- 元服务
- 小红书小程序

注意：H5 平台不支持保存到相册功能

## 实现细节

### 图片保存流程

现在使用通用的 `saveMultipleImagesToAlbum()` 工具函数：

1. 调用 `saveMultipleImagesToAlbum(imageUrls)`
2. 工具函数内部处理：
   - 检查图片数组是否存在
   - 检查并申请相册写入权限
   - 逐一保存每张图片到相册
   - 实时更新保存进度
   - 统计成功和失败数量
   - 显示最终保存结果

### 权限检查流程

1. 使用 `uni.getSetting()` 检查当前权限状态
2. 如果已授权，直接执行保存操作
3. 如果未授权，使用 `uni.authorize()` 申请权限
4. 如果用户拒绝，显示说明弹窗并引导到设置页面

### 批量保存特点

- **顺序保存**：按顺序逐一保存，避免并发问题
- **进度反馈**：实时显示 "保存中 x/总数" 的进度信息
- **容错处理**：单张图片保存失败不影响其他图片
- **结果统计**：详细统计成功和失败的数量
- **用户提示**：根据保存结果显示不同的提示信息

### 错误处理

- 权限相关错误：引导用户授权
- 网络相关错误：提示网络问题
- 文件相关错误：提示文件问题
- 其他错误：通用错误提示

## 页面交互

### 图片展示
- 支持图片轮播查看
- 显示当前图片索引
- 支持手势滑动切换

### 保存操作
- 一键保存所有图片
- 保存过程中显示进度
- 保存完成后显示结果统计

### 其他功能
- 复制当前图片链接
- 返回上一页
- 分享功能（如果支持）

## 数据结构

页面接收的数据格式：
```typescript
interface AlbumData {
  images: string[]      // 图片URL数组
  title?: string        // 相册标题
  description?: string  // 相册描述
  // 其他相关信息...
}
```

## 性能优化

1. **图片懒加载**：大量图片时使用懒加载
2. **内存管理**：及时释放不需要的图片资源
3. **批量限制**：对于大量图片，可以分批保存
4. **错误重试**：失败的图片可以提供重试机制

## 未来扩展

可以考虑添加的功能：
- 选择性保存（勾选要保存的图片）
- 图片预览和编辑
- 保存到指定相册
- 图片压缩选项
- 保存格式选择
