import type { VipVideoData, VipVideoParseParams, VipVideoRawResponse } from './types/vipvideo'
import { http } from '@/http/http'

/**
 * VIP视频/图集解析
 * 支持多平台视频和图集内容解析
 * 统一返回格式，兼容视频和图集两种类型
 */
export async function parseVipVideo(params: VipVideoParseParams): Promise<VipVideoData> {
  // 使用项目封装的http请求库
  const response = await http.get<VipVideoRawResponse['data']>('/vip/video', {
    url: params.url,
  })

  const rawData = response.data

  // 转换为统一格式
  const transformedData: VipVideoData = {
    title: rawData.title || '',
    cover: rawData.cover || '',
  }

  // 只在有值时添加可选字段
  if (rawData.author) {
    transformedData.author = rawData.author
  }
  if (rawData.like) {
    transformedData.like = rawData.like
  }

  // 判断是视频还是图集
  if (rawData.url && !rawData.images) {
    // 视频类型
    transformedData.type = 'video'
    transformedData.video_url = rawData.url
  }
  else if (rawData.images && Array.isArray(rawData.images) && rawData.images.length > 0) {
    // 图集类型
    transformedData.type = 'album'
    transformedData.images = rawData.images
    transformedData.count = rawData.images.length
  }
  else {
    throw new Error('无法识别内容类型：缺少视频链接或图片数组')
  }

  return transformedData
}

/**
 * 批量解析VIP视频/图集
 * @param urls 要解析的URL数组
 * @returns 解析结果数组
 */
export async function parseVipVideoBatch(urls: string[]): Promise<VipVideoData[]> {
  const promises = urls.map(url => parseVipVideo({ url }))
  return Promise.all(promises)
}


