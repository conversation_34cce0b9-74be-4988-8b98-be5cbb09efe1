/**
 * VIP视频解析API相关类型定义
 * 支持多平台视频和图集内容解析
 */

// 内容类型枚举
export enum VipVideoContentType {
  VIDEO = 'video',
  ALBUM = 'album',
}

// VIP视频内容数据（统一格式）
export interface VipVideoData {
  title: string
  cover: string
  type: 'video' | 'album'

  // 视频相关字段（当type为video时必需）
  video_url?: string

  // 图集相关字段（当type为album时必需）
  images?: string[]
  count?: number

  // 可选的扩展字段
  author?: string
  like?: string | number
  time?: string | number
  description?: string
  duration?: number
  platform?: string
}

// VIP视频API原始响应数据格式
export interface VipVideoRawResponse {
  code: number
  message: string
  data: {
    title: string
    cover: string

    // 视频字段
    url?: string

    // 图集字段
    images?: string[]

    // 可选的扩展字段
    author?: string
    like?: string | number
    time?: string | number
    description?: string
    duration?: number
    platform?: string
  }
}

// VIP视频解析请求参数
export interface VipVideoParseParams {
  url: string
  // 可选配置参数
  options?: {
    // 是否获取高清版本
    hd?: boolean
    // 是否获取封面图
    includeCover?: boolean
    // 是否获取作者信息
    includeAuthor?: boolean
    // 超时时间（毫秒）
    timeout?: number
    // 重试次数
    retryCount?: number
  }
}

// 批量解析参数
export interface VipVideoBatchParseParams {
  urls: string[]
  options?: VipVideoParseParams['options']
}

// 解析结果统计
export interface VipVideoParseStats {
  total: number
  success: number
  failed: number
  videos: number
  albums: number
}

// 批量解析结果
export interface VipVideoBatchParseResult {
  results: (VipVideoData | null)[]
  stats: VipVideoParseStats
  errors: Array<{
    url: string
    error: string
  }>
}

// 支持的平台配置
export interface PlatformConfig {
  name: string
  domains: string[]
  supportVideo: boolean
  supportAlbum: boolean
  maxRetry: number
  timeout: number
}

// 类型守卫函数
export function isVipVideoData(data: any): data is VipVideoData {
  return data
    && typeof data.title === 'string'
    && typeof data.cover === 'string'
    && (data.type === 'video' || data.type === 'album')
}

export function isVipVideoVideo(data: VipVideoData): data is VipVideoData & { video_url: string } {
  return data.type === 'video' && !!data.video_url
}

export function isVipVideoAlbum(data: VipVideoData): data is VipVideoData & { images: string[], count: number } {
  return data.type === 'album' && !!data.images && !!data.count
}

// 获取内容类型的工具函数
export function getVipVideoContentType(data: VipVideoData): VipVideoContentType {
  return data.type === 'video' ? VipVideoContentType.VIDEO : VipVideoContentType.ALBUM
}

// 验证URL格式
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  }
  catch {
    return false
  }
}

// 清理和标准化URL
export function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    // 移除不必要的查询参数
    const paramsToKeep = ['v', 'video_id', 'id', 'aweme_id']
    const newSearchParams = new URLSearchParams()

    paramsToKeep.forEach((param) => {
      const value = urlObj.searchParams.get(param)
      if (value) {
        newSearchParams.set(param, value)
      }
    })

    urlObj.search = newSearchParams.toString()
    return urlObj.toString()
  }
  catch {
    return url
  }
}

// 错误类型定义
export class VipVideoError extends Error {
  constructor(
    message: string,
    public code?: string,
    public url?: string,
    public platform?: string,
  ) {
    super(message)
    this.name = 'VipVideoError'
  }
}

// 常见错误代码
export enum VipVideoErrorCode {
  INVALID_URL = 'INVALID_URL',
  UNSUPPORTED_PLATFORM = 'UNSUPPORTED_PLATFORM',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PARSE_ERROR = 'PARSE_ERROR',
  TIMEOUT = 'TIMEOUT',
  RATE_LIMITED = 'RATE_LIMITED',
  CONTENT_NOT_FOUND = 'CONTENT_NOT_FOUND',
  PRIVATE_CONTENT = 'PRIVATE_CONTENT',
}

// API响应状态码
export enum VipVideoApiCode {
  SUCCESS = 0,
  INVALID_PARAMS = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  RATE_LIMITED = 429,
  SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}
