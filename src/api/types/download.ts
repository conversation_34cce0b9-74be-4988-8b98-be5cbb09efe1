/**
 * 下载API相关类型定义
 */

/**
 * 下载请求参数
 */
export interface DownloadRequest {
  /** 原始媒体文件URL */
  url: string
}

/**
 * 下载响应数据类型
 */
export interface DownloadResponse {
  /** 响应状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 下载的文件信息 */
  data?: {
    /** 文件路径 */
    filePath: string
    /** 文件大小（字节） */
    fileSize?: number
    /** 文件类型 */
    fileType?: string
    /** 原始URL */
    originalUrl: string
  }
}

/**
 * 下载错误类型
 */
export interface DownloadError {
  /** 错误码 */
  code: number
  /** 错误消息 */
  message: string
  /** 错误详情 */
  details?: any
}
