# API 接口层

这个目录包含所有的 API 接口定义和相关的类型声明。

## 文件结构

- `douyin.ts` - 抖音视频解析接口
- `vipvideo.ts` - VIP视频解析接口（支持多平台）
- `video.ts` - 通用视频处理接口  
- `login.ts` - 用户认证相关接口
- `foo.ts` - 示例接口（简单版本）
- `foo-alova.ts` - 示例接口（alova版本）
- `types/` - 类型定义目录
  - `douyin.ts` - 抖音相关类型定义
  - `vipvideo.ts` - VIP视频解析相关类型定义
  - `login.ts` - 登录相关类型定义
  - `download.ts` - 下载API相关类型定义

## 重要更新

### 下载API接口 (video.ts - downloadMediaFile)

新增了通过后端代理下载媒体文件的API接口，用于解决跨域和防盗链问题。

**API端点：** `/api/download?url={原始URL}`

**使用场景：**
- 保存视频到相册时需要先下载
- 保存图片到相册时需要先下载
- 绕过防盗链限制

**使用示例：**
```typescript
import { downloadMediaFile } from '@/api/video'

try {
  const filePath = await downloadMediaFile('https://example.com/video.mp4')

  // 可以直接用于保存到相册
  uni.saveVideoToPhotosAlbum({ filePath })
} catch (error) {
  // 处理下载失败
}
```

**错误处理：**
- 400: URL参数无效
- 422: 下载的文件格式无效（可能是链接已失效）
- 500: 网络错误或服务器错误

### VIP视频解析接口 (vipvideo.ts)



**API 端点：** `/api/vip/video?url=<视频链接>`

**响应格式：**

视频类型：
```json
{
  "code": 0,
  "message": "OK",
  "data": {
    "title": "视频标题",
    "cover": "封面图片URL",
    "url": "视频下载链接",
    "images": null
  }
}
```

图集类型：
```json
{
  "code": 0,
  "message": "OK", 
  "data": {
    "title": "图集标题",
    "cover": "封面图片URL",
    "url": null,
    "images": ["图片1URL", "图片2URL", "..."]
  }
}
```

## 使用规范

1. **接口命名**：使用动词+名词的形式，如 `parseDouyin`、`parseVipVideo`
2. **类型定义**：所有接口都应有对应的 TypeScript 类型定义
3. **错误处理**：统一使用项目的错误处理机制
4. **文档注释**：使用 JSDoc 格式添加详细的接口说明

## 使用示例

### VIP视频解析

```typescript
import { parseVipVideo } from '@/api/vipvideo'

// 直接解析视频，后端会自动识别平台
const result = await parseVipVideo({ url })

if (result.type === 'video') {
  console.log('视频链接:', result.video_url)
} else {
  console.log('图片数量:', result.count)
  console.log('图片列表:', result.images)
}
```

### 批量解析

```typescript
import { parseVipVideoBatch } from '@/api/vipvideo'

const urls = ['url1', 'url2', 'url3']
const results = await parseVipVideoBatch(urls)
```

### 抖音解析（保持兼容）

```typescript
import { parseDouyin } from '@/api/douyin'

const result = await parseDouyin({ url: 'https://...' })
```

## 迁移指南

如果你之前使用的是 `xiaohongshu.ts`：

```typescript
// 旧代码
import { parseXiaohongshu } from '@/api/xiaohongshu'
const result = await parseXiaohongshu({ url })

// 新代码
import { parseVipVideo } from '@/api/vipvideo'
const result = await parseVipVideo({ url })
```

新的 `parseVipVideo` 函数返回的数据结构更加统一和完整。

## 环境配置

相关的环境变量配置在 `env/` 目录中：

- `VITE_API_BASE_URL` - API基础地址
- `VITE_VIP_VIDEO_API_TIMEOUT` - VIP视频API超时时间
- `VITE_VIP_VIDEO_API_RETRY_COUNT` - 重试次数

## 错误处理

所有API都使用统一的错误处理机制：

```typescript
try {
  const result = await parseVipVideo({ url })
  // 处理成功结果
} catch (error) {
  if (error instanceof VipVideoError) {
    // 处理特定的VIP视频错误
  } else {
    // 处理其他错误
  }
}
```

## 类型安全

所有接口都提供完整的 TypeScript 类型支持：

```typescript
import type { VipVideoData, VipVideoContentType } from '@/api/types/vipvideo'
import { isVipVideoVideo, isVipVideoAlbum } from '@/api/types/vipvideo'

function handleResult(data: VipVideoData) {
  if (isVipVideoVideo(data)) {
    // TypeScript 知道这里 data.video_url 存在
    // 处理视频数据
  } else if (isVipVideoAlbum(data)) {
    // TypeScript 知道这里 data.images 和 data.count 存在
    // 处理图片数据
  }
}
```
