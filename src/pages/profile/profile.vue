<route lang="jsonc">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "我的",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useUserStore } from '@/store/user'
import { getSafeAreaStyle } from '@/utils/safeArea'
import { useToast } from 'wot-design-uni'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 获取用户状态管理
const userStore = useUserStore()

// 获取 toast 组件
const toast = useToast()

// 定义加载状态变量
const isNavigating = ref(false)

// 分享配置
const shareConfig = {
  title: '趣水印 - 一键去除短视频水印',
  summary: '快速去除短视频水印，支持多平台视频处理',
  imageUrl: '/static/images/ppxlogo.png',
  href: '/pages/index/index'
}

// 分享面板相关状态
const showSharePanel = ref<boolean>(false)
const shareItems = ref([
  {
    iconUrl: '//img12.360buyimg.com/imagetools/jfs/t1/122016/33/6657/1362/5f0692a1E8708d245/e47299e5945a6956.png',
    title: '微信好友'
  },
  {
    iconUrl: 'https://img14.360buyimg.com/imagetools/jfs/t1/111572/11/11734/1245/5f0692a1E39d13d21/b35dfe9243bd6c2a.png',
    title: '微信朋友圈'
  },
  {
    iconUrl: 'https://img14.360buyimg.com/imagetools/jfs/t1/134807/4/3950/1256/5f069336E76949e27/d20641da8e699f07.png',
    title: '微信收藏'
  }
])

// 显示微型加载动画并导航（带错误处理）
function navigateWithAnimation(url: string) {
  isNavigating.value = true

  setTimeout(() => {
    isNavigating.value = false
    uni.navigateTo({
      url,
      animationType: 'slide-in-right',
      animationDuration: 300,
      success: () => {
        console.log('跳转成功:', url)
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  }, 300)
}

// 帮助与反馈（分包路径）
function navigateToHelp() {
  navigateWithAnimation('/pages-sub/help/help')
}

// 处理分享 - 暴露给页面
// #ifdef MP-WEIXIN
function onShareAppMessage() {
  return {
    title: shareConfig.title,
    desc: shareConfig.summary,
    path: shareConfig.href,
    imageUrl: shareConfig.imageUrl,
  }
}

// 同样支持分享到朋友圈
function onShareTimeline() {
  return {
    title: shareConfig.title + '，快速处理各平台视频',
    query: 'from=timeline',
    imageUrl: shareConfig.imageUrl,
  }
}

// 处理添加到收藏
function onAddToFavorites() {
  return {
    title: shareConfig.title,
    imageUrl: shareConfig.imageUrl,
    query: 'from=favorites'
  }
}

defineExpose({
  onShareAppMessage,
  onShareTimeline,
  onAddToFavorites,
})
// #endif

// 显示分享面板
function showShareActions() {
  showSharePanel.value = true
}

// 关闭分享面板
function closeSharePanel() {
  showSharePanel.value = false
}

// 处理分享点击
function handleShareClick(item: any) {
  closeSharePanel()

  // 添加点击反馈
  uni.vibrateShort({
    type: 'light'
  })

  // 根据不同的分享类型执行不同的分享逻辑
  switch (item.title) {
    case '微信好友':
      shareToWechatFriend()
      break
    case '微信朋友圈':
      shareToWechatMoments()
      break
    case '微信收藏':
      shareToWechatFavorites()
      break
    default:
      toast.show('暂不支持该分享方式')
      break
  }
}



// 分享到微信好友
function shareToWechatFriend() {
  // #ifdef MP-WEIXIN
  // 使用 button 的 open-type="share" 已经处理了分享逻辑
  // 这里只需要关闭面板，实际分享由 button 触发
  console.log('分享到微信好友')
  // #endif

  // #ifndef MP-WEIXIN
  toast.show('当前环境不支持微信分享')
  // #endif
}

// 分享到微信朋友圈
function shareToWechatMoments() {
  // #ifdef MP-WEIXIN
  // 使用 button 的 open-type="share" 已经处理了朋友圈分享
  // 实际分享由 button 触发，内容由 onShareTimeline 提供
  console.log('分享到微信朋友圈')
  // #endif

  // #ifndef MP-WEIXIN
  toast.show('当前环境不支持微信朋友圈分享')
  // #endif
}

// 分享到微信收藏
function shareToWechatFavorites() {
  // #ifdef MP-WEIXIN
  // 使用 button 的 open-type="addToFavorites" 已经处理了收藏功能
  // 实际收藏由 button 触发
  console.log('添加到微信收藏')
  // #endif

  // #ifndef MP-WEIXIN
  toast.show('当前环境不支持微信收藏')
  // #endif
}

// 页面加载时确保分享功能已启用
onMounted(() => {
  // #ifdef MP-WEIXIN
  // 使用 wx.showShareMenu 确保分享菜单可用
  wx.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline'],
    success: () => {
      console.log('分享菜单初始化成功')
    },
    fail: (err: any) => {
      console.error('分享菜单初始化失败:', err)
    }
  })
  // #endif
})
</script>

<template>
  <view class="mx-auto bg-gray-50 px-4 flex flex-col" :style="{ ...safeAreaStyle, minHeight: '100vh' }">
    <!-- 微型加载动画 -->
    <view v-if="isNavigating" class="mini-loading" />

    <!-- 主要内容区域 -->
    <view class="flex-1">
      <!-- 用户资料卡片 -->
      <view class="mb-6 rounded-2xl bg-white p-5 shadow-sm">
        <view class="flex items-center">
          <view class="mr-4 h-16 w-16 flex items-center justify-center overflow-hidden rounded-full bg-transparent">
            <!-- 使用logo图片作为头像 -->
            <image src="/static/images/ppxlogo.png" mode="aspectFit" class="h-full w-full" />
          </view>
          <view class="flex-1">
            <text class="mb-1 text-lg text-gray-800 font-medium">
              趣水印
            </text>
            <text class="text-sm text-gray-500">
              Version: 1.0.0
            </text>
          </view>
        </view>
      </view>

      <!-- 其他设置菜单 -->
      <view class="mb-6 rounded-2xl bg-white py-1 shadow-sm">
        <view class="menu-item flex items-center px-5 py-4 active:bg-gray-50" @click="navigateToHelp">
          <view class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-gray-100">
            <text class="i-carbon-help text-gray-500" />
          </view>
          <text class="text-gray-800 font-medium">
            帮助与反馈
          </text>
          <text class="i-carbon-chevron-right ml-auto text-gray-400" />
        </view>

        <!-- 分享应用 -->
        <view class="menu-item flex items-center px-5 py-4 active:bg-gray-50" @click="showShareActions">
          <view class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-gray-100">
            <text class="i-carbon-share text-gray-500" />
          </view>
          <text class="text-gray-800 font-medium">
            分享应用
          </text>
          <text class="i-carbon-chevron-right ml-auto text-gray-400" />
        </view>
      </view>
    </view>

    <!-- 应用信息 - 固定在底部 -->
    <view class="mt-auto pt-8 pb-6 text-center">
      <text class="text-xs text-gray-400">
        趣水印 v1.0.0
      </text>
      <text class="mt-1 block text-xs text-gray-400">
        © 2025 迷失
      </text>
    </view>

    <!-- 分享面板 -->
    <wd-action-sheet
      v-model="showSharePanel"
      cancel-text="取消"
      @close="closeSharePanel"
    >
      <view class="share-panel">
        <!-- 微信好友分享 -->
        <button
          class="share-item-button"
          open-type="share"
          @click="handleShareClick(shareItems[0])"
        >
          <view class="share-item">
            <image :src="shareItems[0].iconUrl" class="share-icon" mode="aspectFit" />
            <text class="share-title">{{ shareItems[0].title }}</text>
          </view>
        </button>

        <!-- 微信朋友圈分享 -->
        <view
          class="share-item"
          @click="handleShareClick(shareItems[1])"
        >
          <image :src="shareItems[1].iconUrl" class="share-icon" mode="aspectFit" />
          <text class="share-title">{{ shareItems[1].title }}</text>
        </view>

        <!-- 微信收藏 -->
        <button
          class="share-item-button"
          open-type="addToFavorites"
          @click="handleShareClick(shareItems[2])"
        >
          <view class="share-item">
            <image :src="shareItems[2].iconUrl" class="share-icon" mode="aspectFit" />
            <text class="share-title">{{ shareItems[2].title }}</text>
          </view>
        </button>
      </view>
    </wd-action-sheet>

    <!-- Toast 组件 -->
    <wd-toast />
  </view>
</template>

<style scoped>
/* 添加微型加载指示器的样式 */
.mini-loading {
  width: 24px;
  height: 24px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  z-index: 9999;
}

@keyframes spin {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.menu-item {
  position: relative;
}

.menu-item::after {
  content: '';
  position: absolute;
  left: 16px;
  right: 16px;
  bottom: 0;
  height: 1px;
  background-color: #f3f4f6;
}

.menu-item:last-child::after {
  display: none;
}

/* wd-action-sheet组件宽度控制 */
:deep(.wd-action-sheet) {
  max-width: 600rpx;
  margin: 0 auto;
}

:deep(.wd-action-sheet__content) {
  max-width: 600rpx;
  margin: 0 auto;
}

/* 分享面板自定义样式 */
.share-panel {
  display: flex;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
}

.share-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 10rpx;
  cursor: pointer;
}

.share-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  margin-bottom: 16rpx;
}

.share-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
}

/* 分享按钮样式 */
.share-item-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
  outline: none;
}

.share-item-button::after {
  border: none;
}

</style>
