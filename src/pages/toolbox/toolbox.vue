<route lang="jsonc">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "工具箱",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<template>
  <view class="min-h-screen px-4 bg-gray-50" :style="safeAreaStyle">
    <!-- 顶部应用栏 -->
    <view class="flex items-center justify-between mt-2 mb-6">
      <text class="text-2xl font-bold text-gray-800">工具箱</text>
    </view>
    
    <!-- 工具列表 -->
    <view class="mb-20">
      <!-- 工具卡片 -->
      <view 
        v-for="(tool, index) in tools" 
        :key="index"
        class="p-4 mb-3 bg-white shadow-sm rounded-xl tool-card border border-gray-200 active:transform active:translate-y-[-2px] transition-all duration-200" 
        @click="navigateToTool(tool.path)"
      >
        <view class="flex">
          <view 
            class="flex-shrink-0 mr-3 w-12 h-12 rounded-xl flex items-center justify-center text-white text-2xl"
            :style="{ background: tool.iconBg }"
          >
            <text :class="tool.icon"></text>
          </view>
          <view class="flex-1">
            <view class="mb-1 text-lg font-semibold text-gray-800 truncate">
              {{ tool.name }}
            </view>
            <view class="text-sm text-gray-500 line-clamp-2">
              {{ tool.description }}
            </view>
          </view>
        </view>
      </view>
      
      <!-- 敬请期待卡片 -->
      <view class="p-4 mb-3 bg-white shadow-sm rounded-xl border border-gray-200 opacity-60">
        <view class="flex">
          <view class="flex-shrink-0 mr-3 w-12 h-12 rounded-xl flex items-center justify-center bg-gray-300 text-white text-2xl">
            <text class="i-carbon-add"></text>
          </view>
          <view class="flex-1">
            <view class="mb-1 text-lg font-semibold text-gray-600">
              更多工具
            </view>
            <view class="text-sm text-gray-400">
              敬请期待更多实用工具...
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 工具列表配置
interface Tool {
  name: string
  description: string
  path: string
  icon: string
  iconBg: string
}

const tools: Tool[] = [
  {
    name: '文件MD5修改',
    description: '快速修改文件的MD5值（支持视频/图片）',
    path: '/pages-sub/video-md5/video-md5',
    icon: 'i-carbon-fingerprint-recognition',
    iconBg: 'linear-gradient(135deg, #10B981, #059669)'
  }
]

// 跳转到工具页面（带错误处理）
const navigateToTool = (path: string) => {
  uni.navigateTo({
    url: path,
    fail: () => {
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      })
    }
  })
}
</script>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
</style>
