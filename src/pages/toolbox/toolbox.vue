<route lang="jsonc">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "工具箱",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { getSafeAreaStyle } from '@/utils/safeArea'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 工具列表配置
interface Tool {
  name: string
  description: string
  path: string
  icon: string
  iconBg: string
}

const tools: Tool[] = [
  {
    name: '文件MD5修改',
    description: '快速修改文件的MD5值（支持视频/图片）',
    path: '/pages-sub/video-md5/video-md5',
    icon: 'i-carbon-fingerprint-recognition',
    iconBg: 'linear-gradient(135deg, #10B981, #059669)',
  },
]

// 跳转到工具页面（带错误处理）
function navigateToTool(path: string) {
  uni.navigateTo({
    url: path,
    fail: () => {
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}
</script>

<template>
  <view class="min-h-screen bg-gray-50 px-4" :style="safeAreaStyle">
    <!-- 顶部应用栏 -->
    <view class="mb-6 mt-2 flex items-center justify-between">
      <text class="text-2xl text-gray-800 font-bold">
        工具箱
      </text>
    </view>

    <!-- 工具列表 -->
    <view class="mb-20">
      <!-- 工具卡片 -->
      <view
        v-for="(tool, index) in tools"
        :key="index"
        class="tool-card mb-3 border border-gray-200 rounded-xl bg-white p-4 shadow-sm transition-all duration-200 active:translate-y-[-2px] active:transform"
        @click="navigateToTool(tool.path)"
      >
        <view class="flex">
          <view
            class="mr-3 h-12 w-12 flex flex-shrink-0 items-center justify-center rounded-xl text-2xl text-white"
            :style="{ background: tool.iconBg }"
          >
            <text :class="tool.icon" />
          </view>
          <view class="flex-1">
            <view class="mb-1 truncate text-lg text-gray-800 font-semibold">
              {{ tool.name }}
            </view>
            <view class="line-clamp-2 text-sm text-gray-500">
              {{ tool.description }}
            </view>
          </view>
        </view>
      </view>

      <!-- 敬请期待卡片 -->
      <view class="mb-3 border border-gray-200 rounded-xl bg-white p-4 opacity-60 shadow-sm">
        <view class="flex">
          <view class="mr-3 h-12 w-12 flex flex-shrink-0 items-center justify-center rounded-xl bg-gray-300 text-2xl text-white">
            <text class="i-carbon-add" />
          </view>
          <view class="flex-1">
            <view class="mb-1 text-lg text-gray-600 font-semibold">
              更多工具
            </view>
            <view class="text-sm text-gray-400">
              敬请期待更多实用工具...
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
</style>
