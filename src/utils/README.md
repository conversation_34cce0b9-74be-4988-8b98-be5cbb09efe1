# 工具函数库

这个目录包含项目中使用的通用工具函数。

## 文件说明

### saveToAlbum.ts

保存媒体文件到相册的通用工具函数，支持保存视频和图片。

#### 主要功能

1. **权限管理**
   - 自动检查相册写入权限
   - 首次使用时申请权限
   - 权限被拒绝时引导用户到设置页面

2. **媒体类型支持**
   - 视频保存：支持网络视频下载和本地保存
   - 图片保存：支持单张和批量保存

3. **用户体验**
   - 可配置的进度提示
   - 详细的保存结果反馈
   - 完善的错误处理

#### API 接口

##### saveSingleMediaToAlbum

保存单个媒体文件到相册

```typescript
saveSingleMediaToAlbum(
  url: string,           // 媒体文件URL
  type: MediaType,       // 媒体类型 (VIDEO | IMAGE)
  options?: SaveOptions  // 保存选项
): Promise<SaveResult>
```

**使用示例：**

```typescript
import { saveSingleMediaToAlbum, MediaType } from '@/utils/saveToAlbum'

// 保存视频
await saveSingleMediaToAlbum(videoUrl, MediaType.VIDEO)

// 保存图片（不显示进度）
await saveSingleMediaToAlbum(imageUrl, MediaType.IMAGE, {
  showProgress: false,
  showResult: true
})
```

##### saveMultipleImagesToAlbum

保存多张图片到相册

```typescript
saveMultipleImagesToAlbum(
  urls: string[],        // 图片URL数组
  options?: SaveOptions  // 保存选项
): Promise<SaveResult>
```

**使用示例：**

```typescript
import { saveMultipleImagesToAlbum } from '@/utils/saveToAlbum'

// 批量保存图片
const result = await saveMultipleImagesToAlbum([
  'https://example.com/image1.jpg',
  'https://example.com/image2.jpg',
  'https://example.com/image3.jpg'
])

console.log(`成功保存 ${result.successCount} 张，失败 ${result.failCount} 张`)
```

#### 类型定义

```typescript
// 媒体类型
enum MediaType {
  VIDEO = 'video',
  IMAGE = 'image'
}

// 保存选项
interface SaveOptions {
  showProgress?: boolean // 是否显示进度提示，默认 true
  showResult?: boolean   // 是否显示结果提示，默认 true
}

// 保存结果
interface SaveResult {
  success: boolean      // 是否全部成功
  successCount: number  // 成功数量
  failCount: number     // 失败数量
  total: number         // 总数量
  message: string       // 结果消息
}
```

#### 特性说明

1. **自动重定向处理**：视频下载会自动处理 HTTP 重定向
2. **文件格式验证**：确保下载的是有效的媒体文件
3. **权限流程完整**：包含完整的权限申请和重新授权流程
4. **错误处理完善**：详细的错误分类和用户友好的提示
5. **进度反馈**：批量保存时显示实时进度

#### 平台兼容性

支持以下平台：
- App (iOS/Android)
- 微信小程序
- 支付宝小程序
- 百度小程序
- 抖音小程序
- QQ小程序
- 快手小程序
- 元服务
- 小红书小程序

注意：H5 平台不支持保存到相册功能

### safeArea.ts

安全区域相关的工具函数，用于处理不同设备的安全区域适配。

### 其他工具函数

根据项目需要，可以在此目录下添加更多通用工具函数，如：
- 网络请求封装
- 数据格式化
- 时间处理
- 本地存储
- 设备信息获取
等等。

## 使用规范

1. **命名规范**：使用驼峰命名法，函数名应清晰表达功能
2. **类型定义**：所有函数都应有完整的 TypeScript 类型定义
3. **错误处理**：必须包含完善的错误处理机制
4. **文档注释**：使用 JSDoc 格式添加详细的函数注释
5. **单一职责**：每个工具函数应专注于单一功能
6. **可测试性**：函数应易于单元测试

## 导入方式

```typescript
// 具名导入（推荐）
import { saveSingleMediaToAlbum, MediaType } from '@/utils/saveToAlbum'
import { getSafeAreaStyle } from '@/utils/safeArea'

// 默认导入
import saveToAlbum from '@/utils/saveToAlbum'
```
