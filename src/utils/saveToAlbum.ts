/**
 * 保存媒体文件到相册的通用工具函数
 * 支持保存单个视频、单张图片或多张图片
 */

import type { DownloadError } from '@/api/types/download'
import { downloadMediaFile } from '@/api/video'

// 媒体类型枚举
export enum MediaType {
  VIDEO = 'video',
  IMAGE = 'image',
}

// 保存选项接口
export interface SaveOptions {
  showProgress?: boolean // 是否显示进度提示
  showResult?: boolean // 是否显示结果提示
}

// 保存结果接口
export interface SaveResult {
  success: boolean
  successCount: number
  failCount: number
  total: number
  message: string
}

/**
 * 检查相册写入权限
 */
async function checkAlbumPermission(): Promise<boolean> {
  return new Promise((resolve) => {
    // 先检查当前权限状态
    uni.getSetting({
      success: (res) => {
        const authSetting = res.authSetting

        // 如果已经授权，直接返回成功
        if (authSetting['scope.writePhotosAlbum'] === true) {
          resolve(true)
          return
        }

        // 如果之前拒绝过授权，引导用户到设置页面
        if (authSetting['scope.writePhotosAlbum'] === false) {
          uni.showModal({
            title: '权限申请',
            content: '需要相册写入权限来保存媒体文件，请在设置中开启',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                uni.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.writePhotosAlbum']) {
                      resolve(true)
                    }
                    else {
                      resolve(false)
                    }
                  },
                  fail: () => {
                    resolve(false)
                  },
                })
              }
              else {
                resolve(false)
              }
            },
          })
          return
        }

        // 首次申请权限
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            resolve(true)
          },
          fail: () => {
            // 用户拒绝授权，显示说明并引导到设置页面
            uni.showModal({
              title: '权限申请',
              content: '需要相册写入权限来保存媒体文件，请在设置中开启',
              confirmText: '去设置',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  uni.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.writePhotosAlbum']) {
                        resolve(true)
                      }
                      else {
                        resolve(false)
                      }
                    },
                    fail: () => {
                      resolve(false)
                    },
                  })
                }
                else {
                  resolve(false)
                }
              },
            })
          },
        })
      },
      fail: () => {
        // 获取设置失败，直接尝试授权
        uni.authorize({
          scope: 'scope.writePhotosAlbum',
          success: () => {
            resolve(true)
          },
          fail: () => {
            resolve(false)
          },
        })
      },
    })
  })
}

/**
 * 下载网络视频到本地
 */
async function downloadVideo(url: string): Promise<string | null> {
  try {
    // 使用封装的API接口下载
    const filePath = await downloadMediaFile(url)

    return filePath
  }
  catch (error) {
    // 根据错误类型显示不同的提示信息
    let errorMessage = '视频下载失败，请检查网络'

    if (error && typeof error === 'object' && 'message' in error) {
      const downloadError = error as DownloadError
      errorMessage = downloadError.message || errorMessage
    }
    else if (error instanceof Error) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000,
    })

    return null
  }
}

/**
 * 保存单个视频到相册
 */
async function saveVideoToPhotosAlbum(filePath: string): Promise<boolean> {
  return new Promise((resolve) => {
    uni.saveVideoToPhotosAlbum({
      filePath,
      success: () => {
        resolve(true)
      },
      fail: () => {
        resolve(false)
      },
    })
  })
}

/**
 * 下载网络图片到本地
 */
async function downloadImage(url: string): Promise<string | null> {
  try {
    // 使用封装的API接口下载
    const filePath = await downloadMediaFile(url)

    return filePath
  }
  catch (error) {
    return null
  }
}

/**
 * 保存单张图片到相册
 */
async function saveImageToPhotosAlbum(url: string): Promise<boolean> {
  return new Promise(async (resolve) => {
    let filePath = url

    // 如果是网络链接，先下载到本地
    if (url.startsWith('http://') || url.startsWith('https://')) {
      const downloadResult = await downloadImage(url)
      if (!downloadResult) {
        resolve(false)
        return
      }
      filePath = downloadResult
    }

    uni.saveImageToPhotosAlbum({
      filePath,
      success: () => {
        resolve(true)
      },
      fail: () => {
        resolve(false)
      },
    })
  })
}

/**
 * 保存单个媒体文件到相册
 * @param url 媒体文件URL
 * @param type 媒体类型
 * @param options 保存选项
 */
export async function saveSingleMediaToAlbum(
  url: string,
  type: MediaType,
  options: SaveOptions = {},
): Promise<SaveResult> {
  const { showProgress = true, showResult = true } = options

  if (!url) {
    const result: SaveResult = {
      success: false,
      successCount: 0,
      failCount: 1,
      total: 1,
      message: '媒体链接不存在',
    }

    if (showResult) {
      uni.showToast({
        title: result.message,
        icon: 'none',
      })
    }

    return result
  }

  try {
    if (showProgress) {
      uni.showLoading({
        title: '保存中...',
        mask: true,
      })
    }

    // 检查相册写入权限
    const authResult = await checkAlbumPermission()
    if (!authResult) {
      if (showProgress)
        uni.hideLoading()
      return {
        success: false,
        successCount: 0,
        failCount: 1,
        total: 1,
        message: '没有相册访问权限',
      }
    }

    let success = false

    if (type === MediaType.VIDEO) {
      // 处理视频保存
      let filePath = url

      // 如果是网络链接，先下载到本地
      if (url.startsWith('http://') || url.startsWith('https://')) {
        const downloadResult = await downloadVideo(url)
        if (!downloadResult) {
          if (showProgress)
            uni.hideLoading()
          return {
            success: false,
            successCount: 0,
            failCount: 1,
            total: 1,
            message: '视频下载失败',
          }
        }
        filePath = downloadResult
      }

      success = await saveVideoToPhotosAlbum(filePath)
    }
    else {
      // 处理图片保存
      success = await saveImageToPhotosAlbum(url)
    }

    if (showProgress)
      uni.hideLoading()

    const result: SaveResult = {
      success,
      successCount: success ? 1 : 0,
      failCount: success ? 0 : 1,
      total: 1,
      message: success ? '保存成功' : '保存失败',
    }

    if (showResult) {
      uni.showToast({
        title: result.message,
        icon: success ? 'success' : 'none',
      })
    }

    return result
  }
  catch (error) {
    if (showProgress)
      uni.hideLoading()

    const result: SaveResult = {
      success: false,
      successCount: 0,
      failCount: 1,
      total: 1,
      message: '保存失败',
    }

    if (showResult) {
      uni.showToast({
        title: result.message,
        icon: 'none',
      })
    }

    return result
  }
}

/**
 * 保存多张图片到相册
 * @param urls 图片URL数组
 * @param options 保存选项
 */
export async function saveMultipleImagesToAlbum(
  urls: string[],
  options: SaveOptions = {},
): Promise<SaveResult> {
  const { showProgress = true, showResult = true } = options

  if (!urls || urls.length === 0) {
    const result: SaveResult = {
      success: false,
      successCount: 0,
      failCount: 0,
      total: 0,
      message: '没有图片可保存',
    }

    if (showResult) {
      uni.showToast({
        title: result.message,
        icon: 'none',
      })
    }

    return result
  }

  try {
    // 检查相册写入权限
    const authResult = await checkAlbumPermission()
    if (!authResult) {
      return {
        success: false,
        successCount: 0,
        failCount: urls.length,
        total: urls.length,
        message: '没有相册访问权限',
      }
    }

    let successCount = 0
    let failCount = 0
    const total = urls.length

    if (showProgress) {
      uni.showLoading({
        title: `保存中 0/${total}`,
      })
    }

    // 逐一保存每张图片
    for (let i = 0; i < urls.length; i++) {
      const imageUrl = urls[i]

      try {
        const success = await saveImageToPhotosAlbum(imageUrl)
        if (success) {
          successCount++
        }
        else {
          failCount++
        }

        if (showProgress) {
          uni.showLoading({
            title: `保存中 ${successCount + failCount}/${total}`,
          })
        }
      }
      catch (error) {
        failCount++
      }
    }

    if (showProgress)
      uni.hideLoading()

    const result: SaveResult = {
      success: successCount === total,
      successCount,
      failCount,
      total,
      message: successCount === total
        ? `成功保存${successCount}张图片`
        : successCount > 0
          ? `成功保存${successCount}张，失败${failCount}张`
          : '保存失败，请检查权限',
    }

    if (showResult) {
      uni.showToast({
        title: result.message,
        icon: result.success ? 'success' : 'none',
        duration: 2000,
      })
    }

    return result
  }
  catch (error) {
    if (showProgress)
      uni.hideLoading()

    const result: SaveResult = {
      success: false,
      successCount: 0,
      failCount: urls.length,
      total: urls.length,
      message: '保存失败',
    }

    if (showResult) {
      uni.showToast({
        title: result.message,
        icon: 'none',
      })
    }

    return result
  }
}
