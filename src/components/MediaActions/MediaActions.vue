<template>
  <wd-card class="action-card">
    <view class="action-content">
      <!-- 按钮行布局 -->
      <view class="button-row">
        <!-- 保存到相册按钮 -->
        <wd-button
          type="primary"
          size="large"
          :loading="isLoading"
          :disabled="isLoading"
          class="action-button primary-button"
          @click="handleSave"
        >
          {{ saveButtonText }}
        </wd-button>

        <!-- 复制链接按钮 -->
        <wd-button
          type="info"
          size="large"
          plain
          class="action-button secondary-button"
          @click="handleCopy"
        >
          {{ copyButtonText }}
        </wd-button>
      </view>

      <!-- 自定义操作按钮 -->
      <slot name="custom-actions" />
    </view>
  </wd-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 媒体类型枚举
enum MediaType {
  VIDEO = 'video',
  ALBUM = 'album'
}

// 组件属性接口
interface Props {
  mediaType: 'video' | 'album'  // 媒体类型
  isLoading?: boolean           // 是否正在加载
  mediaData?: {                 // 媒体数据
    video_url?: string
    images?: string[]
    [key: string]: any
  }
}

// 事件接口
interface Emits {
  save: []      // 保存事件
  copy: []      // 复制事件
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})

const emit = defineEmits<Emits>()

// 计算保存按钮文本
const saveButtonText = computed(() => {
  if (props.isLoading) {
    return props.mediaType === MediaType.VIDEO ? '保存中...' : '保存中...'
  }
  return props.mediaType === MediaType.VIDEO ? '保存到相册' : '保存所有图片'
})

// 计算复制按钮文本
const copyButtonText = computed(() => {
  return props.mediaType === MediaType.VIDEO ? '复制视频链接' : '复制当前图片链接'
})

// 处理保存操作
function handleSave() {
  if (props.isLoading) return
  
  // 检查是否有可保存的内容
  if (props.mediaType === MediaType.VIDEO) {
    if (!props.mediaData?.video_url) {
      uni.showToast({
        title: '视频链接不存在',
        icon: 'none',
      })
      return
    }
  } else {
    if (!props.mediaData?.images?.length) {
      uni.showToast({
        title: '没有图片可保存',
        icon: 'none',
      })
      return
    }
  }
  
  emit('save')
}

// 处理复制操作
function handleCopy() {
  // 检查是否有可复制的内容
  if (props.mediaType === MediaType.VIDEO) {
    if (!props.mediaData?.video_url) {
      uni.showToast({
        title: '视频链接不存在',
        icon: 'none',
      })
      return
    }
  } else {
    if (!props.mediaData?.images?.length) {
      uni.showToast({
        title: '没有图片可复制',
        icon: 'none',
      })
      return
    }
  }
  
  emit('copy')
}
</script>

<style scoped>
.action-card {
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}

.action-content {
  padding: 32rpx;
}

/* 按钮行布局 */
.button-row {
  display: flex;
  gap: 24rpx;
  width: 100%;
}

/* 按钮自适应宽度 */
.action-button {
  flex: 1;
  min-width: 0; /* 防止文字溢出 */
}

/* 自定义按钮样式 */
:deep(.wd-button) {
  border-radius: 12rpx;
  font-weight: 500;
}

:deep(.wd-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

:deep(.wd-button--info.wd-button--plain) {
  color: #667eea;
  border-color: #667eea;
  background: transparent;
}

:deep(.wd-button--info.wd-button--plain:active) {
  background: rgba(102, 126, 234, 0.1);
}

/* 加载状态样式 */
:deep(.wd-button--loading) {
  opacity: 0.7;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .action-content {
    padding: 24rpx;
  }

  .button-row {
    gap: 16rpx;
  }
}
</style>
