# MediaActions 媒体操作组件

通用的媒体操作按钮组件，用于视频和图集的保存、复制等操作。

## 功能特性

- **通用性**：同时支持视频和图集操作
- **智能文本**：根据媒体类型自动调整按钮文本
- **加载状态**：支持操作过程中的加载状态显示
- **数据验证**：自动检查媒体数据有效性
- **事件驱动**：通过事件与父组件通信
- **可扩展**：支持通过插槽添加自定义操作

## 支持的操作

### 基础操作
- **保存到相册**：保存视频或批量保存图片到系统相册
- **复制链接**：复制视频链接或当前图片链接

### 智能适配
- **视频模式**：显示"保存到相册"和"复制视频链接"
- **图集模式**：显示"保存所有图片"和"复制当前图片链接"

## 使用方法

### 视频操作

```vue
<template>
  <MediaActions
    media-type="video"
    :is-loading="isLoading"
    :media-data="videoData"
    @save="handleSave"
    @copy="handleCopy"
  />
</template>

<script setup>
import MediaActions from '@/components/MediaActions/MediaActions.vue'

const videoData = {
  video_url: 'https://example.com/video.mp4'
}

const isLoading = ref(false)

async function handleSave() {
  isLoading.value = true
  try {
    // 保存视频逻辑
    await saveVideo()
  } finally {
    isLoading.value = false
  }
}

function handleCopy() {
  // 复制视频链接逻辑
  copyVideoUrl()
}
</script>
```

### 图集操作

```vue
<template>
  <MediaActions
    media-type="album"
    :is-loading="isSaving"
    :media-data="albumData"
    @save="handleSaveAll"
    @copy="handleCopyImage"
  />
</template>

<script setup>
import MediaActions from '@/components/MediaActions/MediaActions.vue'

const albumData = {
  images: ['url1', 'url2', 'url3']
}

const isSaving = ref(false)

async function handleSaveAll() {
  isSaving.value = true
  try {
    // 批量保存图片逻辑
    await saveAllImages()
  } finally {
    isSaving.value = false
  }
}

function handleCopyImage() {
  // 复制当前图片链接逻辑
  copyCurrentImageUrl()
}
</script>
```

### 自定义操作

```vue
<template>
  <MediaActions
    media-type="video"
    :is-loading="isLoading"
    :media-data="videoData"
    @save="handleSave"
    @copy="handleCopy"
  >
    <template #custom-actions>
      <wd-button
        type="primary"
        size="large"
        block
        plain
        class="mt-4"
        @click="handleShare"
      >
        分享视频
      </wd-button>
    </template>
  </MediaActions>
</template>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `mediaType` | `'video' \| 'album'` | - | 媒体类型，必填 |
| `isLoading` | `boolean` | `false` | 是否正在加载 |
| `mediaData` | `MediaData` | `undefined` | 媒体数据对象 |

### MediaData 类型定义

```typescript
interface MediaData {
  video_url?: string    // 视频链接（视频类型时需要）
  images?: string[]     // 图片数组（图集类型时需要）
  [key: string]: any    // 其他扩展字段
}
```

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `save` | - | 保存操作触发 |
| `copy` | - | 复制操作触发 |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| `custom-actions` | 自定义操作按钮区域 |

## 按钮文本映射

### 保存按钮
- **视频模式**：
  - 正常状态：`保存到相册`
  - 加载状态：`保存中...`
- **图集模式**：
  - 正常状态：`保存所有图片`
  - 加载状态：`保存中...`

### 复制按钮
- **视频模式**：`复制视频链接`
- **图集模式**：`复制当前图片链接`

## 数据验证

组件会自动进行数据验证：

### 视频模式
- 检查 `mediaData.video_url` 是否存在
- 无效时显示"视频链接不存在"提示

### 图集模式
- 检查 `mediaData.images` 数组是否存在且不为空
- 无效时显示相应的错误提示

## 样式特性

### 布局设计
- 使用 `wd-card` 作为容器
- 按钮采用块级布局，充分利用空间
- 按钮间距统一，视觉协调

### 视觉效果
- 主按钮使用渐变背景
- 次按钮使用边框样式
- 支持加载状态的透明度变化
- 响应式设计，适配不同屏幕

### 交互反馈
- 按钮点击有视觉反馈
- 加载状态下按钮不可点击
- 数据无效时自动禁用

## 使用场景

1. **视频详情页**：提供视频保存和链接复制功能
2. **图集详情页**：提供批量图片保存和单图复制功能
3. **媒体预览**：在预览界面提供快捷操作
4. **搜索结果**：在搜索结果中提供操作入口

## 扩展性

### 自定义按钮
通过 `custom-actions` 插槽可以添加更多操作按钮：
- 分享功能
- 收藏功能
- 编辑功能
- 删除功能

### 样式定制
支持通过 CSS 变量自定义样式：
- 按钮颜色
- 间距大小
- 圆角半径
- 字体大小

### 功能扩展
可以通过继承或组合的方式扩展功能：
- 添加更多媒体类型支持
- 增加批量操作模式
- 集成第三方分享SDK
- 添加操作历史记录

## 注意事项

1. **事件处理**：父组件需要正确处理 `save` 和 `copy` 事件
2. **加载状态**：建议在异步操作时正确设置 `isLoading` 状态
3. **数据格式**：确保 `mediaData` 包含对应媒体类型所需的字段
4. **错误处理**：组件只负责基础验证，复杂的错误处理需要在父组件中实现

## 更新日志

- **v1.0.0**：初始版本
  - 支持视频和图集两种媒体类型
  - 基础的保存和复制功能
  - 完整的加载状态支持
  - 自定义操作插槽
