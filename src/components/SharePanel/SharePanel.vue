<script setup lang="ts">
import { computed } from 'vue'
import { useToast } from 'wot-design-uni'

// 定义组件属性
interface Props {
  modelValue: boolean
  shareConfig: {
    title: string
    summary: string
    imageUrl: string
    href: string
  }
  customItems?: {
    friend?: { iconUrl: string, title: string }
    timeline?: { iconUrl: string, title: string }
    favorites?: { iconUrl: string, title: string }
  }
}

// 定义事件
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'share', type: 'friend' | 'timeline' | 'favorites'): void
}

const props = withDefaults(defineProps<Props>(), {
  customItems: () => ({}),
})

const emit = defineEmits<Emits>()

// 获取 toast 组件
const toast = useToast()

// 控制显示状态
const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// 默认分享项配置
const defaultShareItems = {
  friend: {
    iconUrl: '//img12.360buyimg.com/imagetools/jfs/t1/122016/33/6657/1362/5f0692a1E8708d245/e47299e5945a6956.png',
    title: '微信好友',
  },
  timeline: {
    iconUrl: 'https://img14.360buyimg.com/imagetools/jfs/t1/111572/11/11734/1245/5f0692a1E39d13d21/b35dfe9243bd6c2a.png',
    title: '微信朋友圈',
  },
  favorites: {
    iconUrl: 'https://img14.360buyimg.com/imagetools/jfs/t1/134807/4/3950/1256/5f069336E76949e27/d20641da8e699f07.png',
    title: '微信收藏',
  },
}

// 合并自定义配置
const shareItems = computed(() => ({
  friend: { ...defaultShareItems.friend, ...props.customItems.friend },
  timeline: { ...defaultShareItems.timeline, ...props.customItems.timeline },
  favorites: { ...defaultShareItems.favorites, ...props.customItems.favorites },
}))

// 关闭面板
function handleClose() {
  emit('update:modelValue', false)
}

// 处理分享点击
function handleShareClick(type: 'friend' | 'timeline' | 'favorites') {
  handleClose()

  // 添加点击反馈
  uni.vibrateShort({
    type: 'light',
  })

  // 根据不同的分享类型执行不同的分享逻辑
  switch (type) {
    case 'friend':
      shareToWechatFriend()
      break
    case 'timeline':
      shareToWechatMoments()
      break
    case 'favorites':
      shareToWechatFavorites()
      break
  }

  // 触发分享事件
  emit('share', type)
}

// 分享到微信好友
function shareToWechatFriend() {
  // #ifdef MP-WEIXIN
  // 使用 button 的 open-type="share" 已经处理了分享逻辑
  console.log('分享到微信好友')
  // #endif

  // #ifndef MP-WEIXIN
  toast.show('当前环境不支持微信分享')
  // #endif
}

// 分享到微信朋友圈
function shareToWechatMoments() {
  // #ifdef MP-WEIXIN
  // 尝试使用系统分享
  if (uni.shareWithSystem) {
    uni.shareWithSystem({
      summary: `${props.shareConfig.title}，${props.shareConfig.summary}`,
      href: props.shareConfig.href,
      success: () => {
        toast.show('请在系统分享菜单中选择朋友圈')
      },
      fail: (err: any) => {
        console.error('系统分享失败:', err)
        // 回退到显示分享菜单
        wx.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage', 'shareTimeline'],
          success: () => {
            toast.show('请点击右上角分享按钮选择朋友圈')
          },
          fail: (menuErr: any) => {
            console.error('显示分享菜单失败:', menuErr)
            toast.show('朋友圈分享功能暂时不可用')
          },
        })
      },
    })
  }
  else {
    // 如果不支持系统分享，显示分享菜单
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        toast.show('请点击右上角分享按钮选择朋友圈')
      },
      fail: (err: any) => {
        console.error('显示分享菜单失败:', err)
        toast.show('朋友圈分享功能暂时不可用')
      },
    })
  }
  // #endif

  // #ifndef MP-WEIXIN
  toast.show('当前环境不支持微信朋友圈分享')
  // #endif
}

// 分享到微信收藏
function shareToWechatFavorites() {
  // #ifdef MP-WEIXIN
  // 微信小程序收藏功能需要通过右上角菜单实现
  wx.showShareMenu({
    withShareTicket: true,
    success: () => {
      toast.show('请点击右上角菜单选择"收藏"功能')
    },
    fail: (err: any) => {
      console.error('显示分享菜单失败:', err)
      toast.show('收藏功能暂时不可用')
    },
  })
  // #endif

  // #ifndef MP-WEIXIN
  toast.show('当前环境不支持微信收藏')
  // #endif
}
</script>

<template>
  <wd-action-sheet
    v-model="visible"
    cancel-text="取消"
    @close="handleClose"
  >
    <view class="share-panel">
      <!-- 微信好友分享 -->
      <button
        class="share-item-button"
        open-type="share"
        @click="handleShareClick('friend')"
      >
        <view class="share-item">
          <image :src="shareItems.friend.iconUrl" class="share-icon" mode="aspectFit" />
          <text class="share-title">
            {{ shareItems.friend.title }}
          </text>
        </view>
      </button>

      <!-- 微信朋友圈分享 -->
      <view
        class="share-item"
        @click="handleShareClick('timeline')"
      >
        <image :src="shareItems.timeline.iconUrl" class="share-icon" mode="aspectFit" />
        <text class="share-title">
          {{ shareItems.timeline.title }}
        </text>
      </view>

      <!-- 微信收藏 -->
      <view
        class="share-item"
        @click="handleShareClick('favorites')"
      >
        <image :src="shareItems.favorites.iconUrl" class="share-icon" mode="aspectFit" />
        <text class="share-title">
          {{ shareItems.favorites.title }}
        </text>
      </view>
    </view>
  </wd-action-sheet>
</template>

<style scoped>
/* 分享面板自定义样式 */
.share-panel {
  display: flex;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
}

.share-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 10rpx;
  cursor: pointer;
}

.share-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  margin-bottom: 16rpx;
}

.share-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
}

/* 分享按钮样式 */
.share-item-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
  outline: none;
}

.share-item-button::after {
  border: none;
}
</style>
