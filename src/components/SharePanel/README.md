# SharePanel 分享面板组件

一个封装了微信小程序分享功能的通用组件，支持分享到微信好友、朋友圈和收藏。

## 功能特性

- ✅ **微信好友分享** - 使用 button open-type="share" 直接触发分享
- ✅ **微信朋友圈分享** - 支持系统分享菜单和右上角菜单分享
- ✅ **微信收藏功能** - 通过右上角菜单实现收藏
- ✅ **自定义配置** - 支持自定义分享内容和图标
- ✅ **事件回调** - 提供分享事件监听
- ✅ **响应式设计** - 适配不同屏幕尺寸

## 基础用法

### 1. 导入组件和组合式函数

```vue
<script setup lang="ts">
import SharePanel from '@/components/SharePanel/SharePanel.vue'
import { createDefaultShare } from '@/composables/useShare'

// 创建分享实例
const shareInstance = createDefaultShare({
  onShareSuccess: (type) => {
    console.log(`分享成功: ${type}`)
  },
  onShareFail: (type, error) => {
    console.error(`分享失败: ${type}`, error)
  }
})

// 获取分享状态和方法
const { showSharePanel, shareConfig, handleShare } = shareInstance
</script>
```

### 2. 使用组件

```vue
<template>
  <view>
    <!-- 触发分享的按钮 -->
    <button @click="showSharePanel = true">分享</button>
    
    <!-- 分享面板组件 -->
    <SharePanel
      v-model="showSharePanel"
      :share-config="shareConfig"
      @share="handleShare"
    />
  </view>
</template>
```

### 3. 配置页面级分享函数

```vue
<script setup lang="ts">
// 生成页面级分享函数
const pageFunctions = shareInstance.generatePageShareFunctions()

// #ifdef MP-WEIXIN
const { onShareAppMessage, onShareTimeline, onAddToFavorites } = pageFunctions

defineExpose({
  onShareAppMessage,
  onShareTimeline,
  onAddToFavorites,
})
// #endif

// 页面加载时初始化分享菜单
onMounted(() => {
  shareInstance.initShareMenu()
})
</script>
```

## 自定义配置

### 自定义分享内容

```vue
<script setup lang="ts">
import { useShare } from '@/composables/useShare'

// 自定义分享配置
const customShareConfig = {
  title: '我的应用',
  summary: '这是一个很棒的应用',
  imageUrl: '/static/images/my-logo.png',
  href: '/pages/home/<USER>'
}

const shareInstance = useShare(customShareConfig, {
  onShareSuccess: (type) => {
    uni.showToast({ title: '分享成功', icon: 'success' })
  }
})
</script>
```

### 自定义分享图标

```vue
<template>
  <SharePanel
    v-model="showSharePanel"
    :share-config="shareConfig"
    :custom-items="{
      friend: { iconUrl: '/static/icons/wechat.png', title: '微信好友' },
      timeline: { iconUrl: '/static/icons/moments.png', title: '朋友圈' },
      favorites: { iconUrl: '/static/icons/favorites.png', title: '收藏' }
    }"
    @share="handleShare"
  />
</template>
```

## API 参考

### SharePanel Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | boolean | false | 控制面板显示/隐藏 |
| shareConfig | ShareConfig | - | 分享配置对象 |
| customItems | CustomItems | {} | 自定义分享项配置 |

### SharePanel Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | boolean | 面板显示状态变化 |
| share | ShareType | 分享类型：'friend' \| 'timeline' \| 'favorites' |

### ShareConfig 接口

```typescript
interface ShareConfig {
  title: string      // 分享标题
  summary: string    // 分享描述
  imageUrl: string   // 分享图片
  href: string       // 分享链接
}
```

### useShare 组合式函数

```typescript
const shareInstance = useShare(config, callbacks)

// 返回值
interface ShareInstance {
  showSharePanel: Ref<boolean>           // 面板显示状态
  shareConfig: ShareConfig               // 分享配置
  showShare: () => void                  // 显示分享面板
  hideShare: () => void                  // 隐藏分享面板
  handleShare: (type: ShareType) => void // 处理分享事件
  generatePageShareFunctions: () => {}   // 生成页面级分享函数
  initShareMenu: () => void              // 初始化分享菜单
}
```

## 注意事项

1. **微信小程序环境** - 组件主要为微信小程序设计，其他平台会显示相应提示
2. **页面级分享** - 需要在页面中配置 onShareAppMessage 等生命周期函数
3. **分享菜单** - 朋友圈和收藏功能需要用户通过右上角菜单操作
4. **图片资源** - 分享图片建议使用本地资源，确保加载速度

## 完整示例

查看 `src/pages/profile/profile.vue` 文件了解完整的使用示例。
