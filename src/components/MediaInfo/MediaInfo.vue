<script setup lang="ts">
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

const props = withDefaults(defineProps<Props>(), {
  showImageCount: false,
})
// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

// 定义组件属性
interface MediaData {
  title?: string
  author?: string
  like?: string | number
  time?: string | number
  description?: string
  duration?: number
  count?: number
  images?: string[]
}

interface Props {
  mediaData?: MediaData
  showImageCount?: boolean // 是否显示图片数量（图集类型时为true）
}

// 格式化时间
function formatTime(time: string | number): string {
  if (typeof time === 'number') {
    return dayjs.unix(time).format('YYYY-MM-DD HH:mm:ss')
  }
  return time
}

// 格式化视频时长
function formatDuration(duration: number): string {
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)
  const seconds = Math.floor(duration % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}
</script>

<template>
  <wd-card class="media-info-card">
    <view class="media-info-content">
      <!-- 标题 -->
      <view v-if="mediaData?.title" class="info-row">
        <text class="info-label">
          标题:
        </text>
        <text class="info-value">
          {{ mediaData.title }}
        </text>
      </view>

      <!-- 创作者 -->
      <view v-if="mediaData?.author" class="info-row">
        <text class="info-label">
          创作者:
        </text>
        <text class="info-value">
          {{ mediaData.author }}
        </text>
      </view>

      <!-- 点赞数量 -->
      <view v-if="mediaData?.like" class="info-row">
        <text class="info-label">
          点赞数量:
        </text>
        <text class="info-value">
          {{ mediaData.like }}
        </text>
      </view>

      <!-- 图片数量（仅图集类型显示） -->
      <view v-if="showImageCount && (mediaData?.count || mediaData?.images?.length)" class="info-row">
        <text class="info-label">
          图片数量:
        </text>
        <text class="info-value">
          {{ mediaData.count || mediaData.images?.length }} 张
        </text>
      </view>

      <!-- 视频时长（仅视频类型显示） -->
      <view v-if="mediaData?.duration" class="info-row">
        <text class="info-label">
          视频时长:
        </text>
        <text class="info-value">
          {{ formatDuration(mediaData.duration) }}
        </text>
      </view>

      <!-- 发布时间 -->
      <view v-if="mediaData?.time" class="info-row">
        <text class="info-label">
          发布时间:
        </text>
        <text class="info-value">
          {{ formatTime(mediaData.time) }}
        </text>
      </view>

      <!-- 描述信息 -->
      <view v-if="mediaData?.description" class="info-row">
        <text class="info-label">
          描述:
        </text>
        <text class="info-value description">
          {{ mediaData.description }}
        </text>
      </view>
    </view>
  </wd-card>
</template>

<style scoped>
.media-info-card {
  margin-bottom: 24rpx;
}

.media-info-content {
  padding: 32rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 16rpx 0;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 160rpx;
  flex-shrink: 0;
  font-size: 28rpx;
  color: #666666;
  font-weight: 600;
  line-height: 1.5;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  word-break: break-all;
}

.info-value.description {
  line-height: 1.6;
  color: #555555;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .info-label {
    width: 140rpx;
    font-size: 26rpx;
  }

  .info-value {
    font-size: 26rpx;
  }
}
</style>
