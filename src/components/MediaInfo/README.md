# MediaInfo 媒体信息组件

通用的媒体信息展示组件，用于显示视频和图集的详细信息。

## 功能特性

- **通用性**：同时支持视频和图集信息展示
- **条件渲染**：只显示有数据的字段，没有数据的字段自动隐藏
- **响应式设计**：适配不同屏幕尺寸
- **类型安全**：完整的 TypeScript 类型支持
- **样式统一**：统一的视觉风格和交互体验

## 支持的信息字段

### 基础信息
- **标题** (`title`) - 媒体标题
- **创作者** (`author`) - 作者/创作者信息

### 互动数据
- **点赞数量** (`like`) - 点赞/喜欢数量，支持字符串和数字类型

### 时间信息
- **发布时间** (`time`) - 发布时间，支持 Unix 时间戳和字符串格式
- **视频时长** (`duration`) - 视频时长（秒），自动格式化为 mm:ss 或 hh:mm:ss

### 内容信息
- **描述** (`description`) - 详细描述信息
- **图片数量** (`count` / `images.length`) - 图集中的图片数量

## 使用方法

### 基础用法

```vue
<template>
  <MediaInfo :media-data="videoData" />
</template>

<script setup>
import MediaInfo from '@/components/MediaInfo/MediaInfo.vue'

const videoData = {
  title: '视频标题',
  author: '创作者名称',
  like: 1000,
  time: 1640995200, // Unix 时间戳
  duration: 120 // 2分钟
}
</script>
```

### 图集用法

```vue
<template>
  <MediaInfo 
    :media-data="albumData" 
    :show-image-count="true" 
  />
</template>

<script setup>
import MediaInfo from '@/components/MediaInfo/MediaInfo.vue'

const albumData = {
  title: '图集标题',
  author: '创作者名称',
  count: 10,
  images: ['url1', 'url2', '...'],
  time: '2024-01-01 12:00:00'
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `mediaData` | `MediaData` | `undefined` | 媒体数据对象 |
| `showImageCount` | `boolean` | `false` | 是否显示图片数量（图集类型时设为 true） |

### MediaData 类型定义

```typescript
interface MediaData {
  title?: string          // 标题
  author?: string         // 创作者
  like?: string | number  // 点赞数量
  time?: string | number  // 发布时间
  description?: string    // 描述
  duration?: number       // 视频时长（秒）
  count?: number          // 图片数量
  images?: string[]       // 图片数组
}
```

## 样式特性

### 布局结构
- 使用 `wd-card` 组件作为容器
- 每个信息项使用 flex 布局
- 标签固定宽度，内容自适应

### 响应式适配
- 在小屏设备上自动调整字体大小
- 标签宽度在小屏时自动缩小
- 内容区域支持自动换行

### 视觉样式
- 标签使用灰色字体，内容使用深色字体
- 描述信息使用特殊的行高和颜色
- 统一的间距和边距设计

## 时间格式化

组件内置了时间格式化功能：

- **Unix 时间戳**：自动转换为 `YYYY-MM-DD HH:mm:ss` 格式
- **字符串时间**：直接显示原始字符串
- **视频时长**：自动格式化为 `mm:ss` 或 `hh:mm:ss` 格式

## 使用场景

1. **视频详情页**：显示视频的完整信息
2. **图集详情页**：显示图集的详细信息
3. **媒体列表**：在列表中展示媒体摘要信息
4. **搜索结果**：显示搜索到的媒体信息

## 扩展性

组件设计时考虑了扩展性：

1. **新增字段**：可以轻松添加新的信息字段
2. **自定义样式**：支持通过 CSS 变量自定义样式
3. **条件显示**：所有字段都支持条件渲染
4. **格式化函数**：可以添加新的数据格式化函数

## 注意事项

1. **数据验证**：组件会自动检查数据是否存在，不存在的字段不会显示
2. **类型安全**：建议使用 TypeScript 以获得更好的类型检查
3. **性能优化**：组件使用了条件渲染，只渲染有数据的字段
4. **样式隔离**：使用了 scoped 样式，不会影响其他组件

## 更新日志

- **v1.0.0**：初始版本，支持基础的媒体信息展示
- 支持视频和图集两种类型
- 完整的 TypeScript 类型支持
- 响应式设计和样式优化
