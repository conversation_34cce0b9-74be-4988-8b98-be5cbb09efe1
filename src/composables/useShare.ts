import { ref } from 'vue'

// 分享配置接口
export interface ShareConfig {
  title: string
  summary: string
  imageUrl: string
  href: string
}

// 分享类型
export type ShareType = 'friend' | 'timeline' | 'favorites'

// 分享事件回调
export interface ShareCallbacks {
  onShareSuccess?: (type: ShareType) => void
  onShareFail?: (type: ShareType, error: any) => void
}

/**
 * 分享组合式函数
 * @param config 分享配置
 * @param callbacks 分享回调
 */
export function useShare(config: ShareConfig, callbacks?: ShareCallbacks) {
  // 分享面板显示状态
  const showSharePanel = ref(false)

  // 显示分享面板
  function showShare() {
    showSharePanel.value = true
  }

  // 隐藏分享面板
  function hideShare() {
    showSharePanel.value = false
  }

  // 处理分享事件
  function handleShare(type: ShareType) {
    console.log(`分享类型: ${type}`)
    
    // 触发成功回调
    callbacks?.onShareSuccess?.(type)
  }

  // 生成页面级分享函数
  function generatePageShareFunctions() {
    // #ifdef MP-WEIXIN
    // 处理分享给好友
    function onShareAppMessage() {
      return {
        title: config.title,
        desc: config.summary,
        path: config.href,
        imageUrl: config.imageUrl,
      }
    }

    // 处理分享到朋友圈
    function onShareTimeline() {
      return {
        title: config.title + '，快速处理各平台视频',
        query: 'from=timeline',
        imageUrl: config.imageUrl,
      }
    }

    // 处理添加到收藏
    function onAddToFavorites() {
      return {
        title: config.title,
        imageUrl: config.imageUrl,
        query: 'from=favorites'
      }
    }

    return {
      onShareAppMessage,
      onShareTimeline,
      onAddToFavorites,
    }
    // #endif

    // #ifndef MP-WEIXIN
    return {}
    // #endif
  }

  // 初始化分享菜单
  function initShareMenu() {
    // #ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
      success: () => {
        console.log('分享菜单初始化成功')
      },
      fail: (err: any) => {
        console.error('分享菜单初始化失败:', err)
      }
    })
    // #endif
  }

  return {
    // 状态
    showSharePanel,
    
    // 方法
    showShare,
    hideShare,
    handleShare,
    generatePageShareFunctions,
    initShareMenu,
    
    // 配置
    shareConfig: config,
  }
}

// 默认分享配置
export const defaultShareConfig: ShareConfig = {
  title: '趣水印 - 一键去除短视频水印',
  summary: '快速去除短视频水印，支持多平台视频处理',
  imageUrl: '/static/images/ppxlogo.png',
  href: '/pages/index/index'
}

// 创建默认分享实例
export function createDefaultShare(callbacks?: ShareCallbacks) {
  return useShare(defaultShareConfig, callbacks)
}
